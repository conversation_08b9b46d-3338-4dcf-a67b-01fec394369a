{"id": "review_270ea7d6dfca4992826a05e3c9499c4d", "templateId": "template_demo_design_review", "templateVersion": "1.0", "type": "design-review", "createdTime": "2025-08-02 23:27:31", "status": "IN_PROGRESS", "reviewItems": [{"itemId": "item_d001", "sequence": 1, "content": "系统架构设计是否合理？", "category": "架构设计", "required": true, "status": "PENDING", "comment": "", "reviewHistory": []}, {"itemId": "item_d002", "sequence": 2, "content": "数据库设计是否符合规范？", "category": "数据库设计", "required": true, "status": "PENDING", "comment": "", "reviewHistory": []}, {"itemId": "item_d003", "sequence": 3, "content": "API接口设计是否完整？", "category": "接口设计", "required": true, "status": "PENDING", "comment": "", "reviewHistory": []}, {"itemId": "item_d004", "sequence": 4, "content": "用户界面设计是否友好？", "category": "UI设计", "required": true, "status": "PENDING", "comment": "", "reviewHistory": []}, {"itemId": "item_d005", "sequence": 5, "content": "系统可扩展性如何？", "category": "可扩展性", "required": false, "status": "PENDING", "comment": "", "reviewHistory": []}, {"itemId": "item_d006", "sequence": 6, "content": "安全设计是否充分？", "category": "安全设计", "required": true, "status": "PENDING", "comment": "", "reviewHistory": []}]}