import request, { type RequestConfig } from '@/utils/request'

// Review status enum
export enum ReviewStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

// Review item status enum
export enum ReviewItemStatus {
  PENDING = 'PENDING',
  PASS = 'PASS',
  FAIL = 'FAIL',
  SKIP = 'SKIP',
}

// Review related interfaces
export interface ChecklistReview {
  id: string
  templateId: string
  templateVersion: string
  type: string
  createdTime: string
  updatedTime: string
  status: ReviewStatus
  reviewer?: string
  reviewItems: ReviewItem[]
  progress: ReviewProgress
}

export interface ReviewItem {
  itemId: string
  sequence: number
  content: string
  required: boolean
  category: string
  status: ReviewItemStatus
  comment: string
  reviewer?: string
  reviewTime?: string
  reviewHistory: ReviewHistory[]
  customFields?: Record<string, any>  // 支持自定义字段
}

export interface ReviewHistory {
  id: string
  reviewer: string
  reviewTime: string
  status: ReviewItemStatus
  comment: string
  action: string
}

export interface ReviewProgress {
  total: number
  completed: number
  passed: number
  failed: number
  skipped: number
  pending: number
  percentage: number
}

export interface CreateReviewRequest {
  templateId: string
  type: string
  reviewer?: string
}

export interface UpdateReviewItemRequest {
  status: ReviewItemStatus
  comment?: string
  reviewer?: string
}

export interface BatchUpdateRequest {
  itemIds: string[]
  status: ReviewItemStatus
  comment?: string
  reviewer?: string
}

export interface ReviewListQuery {
  page?: number
  size?: number
  type?: string
  status?: ReviewStatus
  reviewer?: string
  startDate?: string
  endDate?: string
}

export interface ReviewListResponse {
  reviews: ChecklistReview[]
  total: number
  page: number
  size: number
}

// Review API service class
class ReviewApiService {
  private readonly baseUrl = '/reviews'

  /**
   * Create review instance
   */
  async createReview(
    reviewRequest: CreateReviewRequest,
    config?: RequestConfig,
  ): Promise<ChecklistReview> {
    // Validate required fields
    if (!reviewRequest.templateId?.trim()) {
      throw new Error('模板ID不能为空')
    }
    if (!reviewRequest.type?.trim()) {
      throw new Error('评审类型不能为空')
    }

    return request.post(this.baseUrl, reviewRequest, {
      showLoading: true,
      showSuccess: false, // 禁用API层面的成功提示，由组件层面统一处理
      ...config,
    })
  }

  /**
   * Get review instance by ID
   */
  async getReview(id: string, config?: RequestConfig): Promise<ChecklistReview> {
    if (!id?.trim()) {
      throw new Error('评审ID不能为空')
    }

    return request.get(`${this.baseUrl}/${id}`, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Get review list with pagination and filters
   */
  async getReviewList(query?: ReviewListQuery, config?: RequestConfig): Promise<ReviewListResponse> {
    const params = new URLSearchParams()
    
    if (query?.page !== undefined) params.append('page', query.page.toString())
    if (query?.size !== undefined) params.append('size', query.size.toString())
    if (query?.type) params.append('type', query.type)
    if (query?.status) params.append('status', query.status)
    if (query?.reviewer) params.append('reviewer', query.reviewer)
    if (query?.startDate) params.append('startDate', query.startDate)
    if (query?.endDate) params.append('endDate', query.endDate)

    const queryString = params.toString()
    const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl

    return request.get(url, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Update review item status
   */
  async updateReviewItem(
    reviewId: string,
    itemId: string,
    updateRequest: UpdateReviewItemRequest,
    config?: RequestConfig,
  ): Promise<ReviewItem> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }
    if (!itemId?.trim()) {
      throw new Error('检查项ID不能为空')
    }
    if (!updateRequest.status) {
      throw new Error('评审状态不能为空')
    }

    // Validate comment for FAIL status
    if (updateRequest.status === ReviewItemStatus.FAIL && !updateRequest.comment?.trim()) {
      throw new Error('不通过时必须填写原因')
    }

    return request.put(`${this.baseUrl}/${reviewId}/items/${itemId}`, updateRequest, {
      showLoading: false, // Don't show loading for individual item updates
      showSuccess: false, // Don't show success message for individual updates
      ...config,
    })
  }

  /**
   * Batch update review items
   */
  async batchUpdateReviewItems(
    reviewId: string,
    batchRequest: BatchUpdateRequest,
    config?: RequestConfig,
  ): Promise<ReviewItem[]> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }
    if (!batchRequest.itemIds || batchRequest.itemIds.length === 0) {
      throw new Error('请选择要更新的检查项')
    }
    if (!batchRequest.status) {
      throw new Error('评审状态不能为空')
    }

    // Validate comment for FAIL status
    if (batchRequest.status === ReviewItemStatus.FAIL && !batchRequest.comment?.trim()) {
      throw new Error('批量设置为不通过时必须填写原因')
    }

    return request.put(`${this.baseUrl}/${reviewId}/items/batch`, batchRequest, {
      showLoading: true,
      showSuccess: true,
      successMessage: `成功更新 ${batchRequest.itemIds.length} 个检查项`,
      ...config,
    })
  }

  /**
   * Get review history
   */
  async getReviewHistory(reviewId: string, config?: RequestConfig): Promise<ReviewHistory[]> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    return request.get(`${this.baseUrl}/${reviewId}/history`, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Complete review
   */
  async completeReview(reviewId: string, payload?: any, config?: RequestConfig): Promise<ChecklistReview> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    return request.put(`${this.baseUrl}/${reviewId}/complete`, payload || {}, {
      showLoading: true,
      showSuccess: true,
      successMessage: '评审已完成',
      ...config,
    })
  }

  /**
   * Cancel review
   */
  async cancelReview(reviewId: string, reason?: string, config?: RequestConfig): Promise<ChecklistReview> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    return request.put(`${this.baseUrl}/${reviewId}/cancel`, { reason }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '评审已取消',
      ...config,
    })
  }

  /**
   * Export review results
   */
  async exportReview(reviewId: string, format: 'excel' | 'pdf' = 'excel', config?: RequestConfig): Promise<Blob> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    return request.get(`${this.baseUrl}/${reviewId}/export?format=${format}`, {
      responseType: 'blob',
      showLoading: true,
      ...config,
    })
  }

  /**
   * Get review statistics
   */
  async getReviewStatistics(
    query?: { type?: string; startDate?: string; endDate?: string },
    config?: RequestConfig,
  ): Promise<any> {
    const params = new URLSearchParams()
    
    if (query?.type) params.append('type', query.type)
    if (query?.startDate) params.append('startDate', query.startDate)
    if (query?.endDate) params.append('endDate', query.endDate)

    const queryString = params.toString()
    const url = queryString ? `${this.baseUrl}/statistics?${queryString}` : `${this.baseUrl}/statistics`

    return request.get(url, {
      showLoading: true,
      ...config,
    })
  }

  /**
   * Auto-save review progress
   */
  async autoSaveReview(reviewId: string, data: Partial<ChecklistReview>, config?: RequestConfig): Promise<void> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    return request.put(`${this.baseUrl}/${reviewId}/autosave`, data, {
      showLoading: false,
      showError: false, // Don't show error for auto-save failures
      showSuccess: false,
      timeout: 5000, // Short timeout for auto-save
      ...config,
    })
  }
}

// Create and export service instance
const reviewApi = new ReviewApiService()

// Export individual methods for backward compatibility
export const createReview = (templateId: string, type: string, reviewer?: string) =>
  reviewApi.createReview({ templateId, type, reviewer })
export const getReview = reviewApi.getReview.bind(reviewApi)
export const getReviewList = reviewApi.getReviewList.bind(reviewApi)
export const updateReviewItem = (reviewId: string, itemId: string, status: ReviewItemStatus, comment?: string, reviewer?: string) =>
  reviewApi.updateReviewItem(reviewId, itemId, { status, comment, reviewer })
export const batchUpdateReviewItems = reviewApi.batchUpdateReviewItems.bind(reviewApi)
export const getReviewHistory = reviewApi.getReviewHistory.bind(reviewApi)
export const completeReview = (reviewId: string, payload?: any) => reviewApi.completeReview(reviewId, payload)
export const cancelReview = reviewApi.cancelReview.bind(reviewApi)
export const exportReview = reviewApi.exportReview.bind(reviewApi)
export const getReviewStatistics = reviewApi.getReviewStatistics.bind(reviewApi)
export const autoSaveReview = reviewApi.autoSaveReview.bind(reviewApi)

// Export service instance
export default reviewApi
