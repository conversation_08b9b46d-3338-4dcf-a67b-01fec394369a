<template>
  <div class="table-cell">
    <!-- 选择列现在由 Element Plus 原生处理 -->

    <!-- 序号列 -->
    <span v-if="column.key === 'sequence'" class="sequence-cell">
      {{ row.sequence }}
    </span>

    <!-- 检查内容列 -->
    <div v-else-if="column.key === 'content'" class="content-cell">
      <p class="content-text">{{ row.content }}</p>
      <div v-if="row.required" class="required-indicator">
        <el-tag type="danger" size="small" effect="plain">
          <el-icon><Star /></el-icon>
          必填
        </el-tag>
      </div>
    </div>

    <!-- 状态列 -->
    <div v-else-if="column.key === 'status'" class="status-cell">
      <!-- 调试信息 -->
      <!-- Debug: editable={{ column.editable }}, readonly={{ readonly }} -->

      <!-- 只读模式或不可编辑时显示标签 -->
      <el-tag
        v-if="readonly"
        :type="getStatusTagType(row.status)"
        :effect="row.status === 'PENDING' ? 'plain' : 'dark'"
        size="small"
      >
        <el-icon>
          <component :is="getStatusIcon(row.status)" />
        </el-icon>
        {{ getStatusText(row.status) }}
      </el-tag>

      <!-- 可编辑模式显示状态选择器 -->
      <div v-else class="status-selector">
        <!-- 紧凑的状态按钮组 -->
        <el-button-group class="compact-status-buttons">
          <el-button
            v-for="status in statusOptions"
            :key="status.value"
            :type="row.status === status.value ? status.type : ''"
            :loading="statusChanging"
            size="small"
            @click="handleStatusChange(status.value)"
            class="status-btn"
            :class="`status-${status.value.toLowerCase()}`"
          >
            <el-icon>
              <component :is="status.icon" />
            </el-icon>
            <span class="status-text">{{ status.label }}</span>
          </el-button>
        </el-button-group>

        <!-- 备注按钮 -->
        <el-button
          v-if="row.status !== 'PENDING' || row.comment"
          size="small"
          type="info"
          text
          @click="editComment"
          class="comment-btn"
          :title="row.comment ? '编辑备注' : '添加备注'"
        >
          <el-icon><EditPen /></el-icon>
          <span v-if="row.comment" class="comment-indicator">•</span>
        </el-button>
      </div>
    </div>

    <!-- 分类列 -->
    <el-tag
      v-else-if="column.key === 'category'"
      v-show="row.category"
      type="info"
      size="small"
      effect="plain"
    >
      {{ row.category }}
    </el-tag>

    <!-- 评审人列 -->
    <span v-else-if="column.key === 'reviewer'" class="reviewer-cell">
      {{ row.reviewer || '-' }}
    </span>

    <!-- 评审时间列 -->
    <span v-else-if="column.key === 'reviewTime'" class="time-cell">
      {{ row.reviewTime ? formatTime(row.reviewTime) : '-' }}
    </span>

    <!-- 备注列 -->
    <div v-else-if="column.key === 'comment'" class="comment-cell">
      <el-input
        v-if="column.editable && editing"
        v-model="editValue"
        type="textarea"
        :rows="2"
        @blur="handleCommentSave"
        @keyup.enter="handleCommentSave"
        size="small"
      />
      <div v-else class="comment-display" @click="startEdit">
        <span v-if="row.comment" class="comment-text">{{ row.comment }}</span>
        <span v-else class="comment-placeholder">点击添加备注</span>
        <el-icon v-if="column.editable" class="edit-icon"><EditPen /></el-icon>
      </div>
    </div>

    <!-- 自定义字段列 -->
    <div v-else-if="isCustomField" class="custom-field-cell">
      <!-- 文本类型 -->
      <el-input
        v-if="column.type === 'text' && column.editable && editing"
        v-model="editValue"
        @blur="handleCustomFieldSave"
        @keyup.enter="handleCustomFieldSave"
        size="small"
      />
      <span
        v-else-if="column.type === 'text'"
        class="text-value"
        @click="column.editable && startEdit()"
      >
        {{ getCustomFieldValue() || '-' }}
        <el-icon v-if="column.editable" class="edit-icon"><EditPen /></el-icon>
      </span>

      <!-- 数字类型 -->
      <el-input-number
        v-else-if="column.type === 'number' && column.editable && editing"
        v-model="editValue"
        @blur="handleCustomFieldSave"
        size="small"
        style="width: 100%"
      />
      <span
        v-else-if="column.type === 'number'"
        class="number-value"
        @click="column.editable && startEdit()"
      >
        {{ getCustomFieldValue() || '-' }}
        <el-icon v-if="column.editable" class="edit-icon"><EditPen /></el-icon>
      </span>

      <!-- 日期类型 -->
      <el-date-picker
        v-else-if="column.type === 'date' && column.editable && editing"
        v-model="editValue"
        type="date"
        @blur="handleCustomFieldSave"
        size="small"
        style="width: 100%"
      />
      <span
        v-else-if="column.type === 'date'"
        class="date-value"
        @click="column.editable && startEdit()"
      >
        {{ formatCustomDate(getCustomFieldValue()) || '-' }}
        <el-icon v-if="column.editable" class="edit-icon"><EditPen /></el-icon>
      </span>

      <!-- 标签类型 -->
      <el-select
        v-else-if="column.type === 'tag' && column.editable && editing"
        v-model="editValue"
        @blur="handleCustomFieldSave"
        @change="handleCustomFieldSave"
        size="small"
        style="width: 100%"
      >
        <el-option
          v-for="option in column.options"
          :key="option"
          :label="option"
          :value="option"
        />
      </el-select>
      <el-tag
        v-else-if="column.type === 'tag' && getCustomFieldValue()"
        size="small"
        @click="column.editable && startEdit()"
      >
        {{ getCustomFieldValue() }}
        <el-icon v-if="column.editable" class="edit-icon"><EditPen /></el-icon>
      </el-tag>
      <span
        v-else-if="column.type === 'tag'"
        class="tag-placeholder"
        @click="column.editable && startEdit()"
      >
        -
        <el-icon v-if="column.editable" class="edit-icon"><EditPen /></el-icon>
      </span>
    </div>

    <!-- 默认显示 -->
    <span v-else class="default-cell">
      {{ getCellValue() || '-' }}
    </span>

    <!-- 状态变更备注对话框 -->
    <el-dialog
      v-model="showCommentDialog"
      :title="`${getStatusText(pendingStatus)} - 添加备注`"
      width="400px"
      :before-close="cancelStatusChange"
    >
      <div class="status-comment-form">
        <el-form :model="statusForm" :rules="statusRules" ref="statusFormRef">
          <el-form-item
            label="备注内容"
            prop="comment"
            :required="isCommentRequired"
          >
            <el-input
              v-model="statusForm.comment"
              type="textarea"
              :rows="3"
              placeholder="请输入备注内容..."
              :maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelStatusChange">取消</el-button>
          <el-button
            type="primary"
            @click="confirmStatusChange"
            :loading="statusChanging"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Star, EditPen, CircleCheck, CircleClose, Remove, Clock } from '@element-plus/icons-vue'
import { type ExtendedReviewItem, type ColumnConfig } from '@/types/table-config'
import { ReviewItemStatus } from '@/api/review'
import { type StatusButtonConfig } from '@/types/defect-config'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  row: ExtendedReviewItem
  column: ColumnConfig
  index: number
  selected?: boolean
  readonly?: boolean
  buttonConfig?: StatusButtonConfig
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  readonly: false
})

// Emits
interface Emits {
  (e: 'cell-change', itemId: string, field: string, value: any): void
  (e: 'status-change', itemId: string, status: ReviewItemStatus, comment?: string): void
}

const emit = defineEmits<Emits>()

// Reactive data
const editing = ref(false)
const editValue = ref<any>('')

// 状态变更相关
const showCommentDialog = ref(false)
const statusChanging = ref(false)
const pendingStatus = ref<ReviewItemStatus>(ReviewItemStatus.PENDING)
const statusFormRef = ref()
const statusForm = ref({
  comment: ''
})

const statusRules = {
  comment: [
    {
      required: true,
      message: '请输入备注内容',
      trigger: 'blur',
      validator: (rule: any, value: string, callback: Function) => {
        if (isCommentRequired.value && (!value || value.trim() === '')) {
          callback(new Error('此状态变更需要填写备注'))
        } else {
          callback()
        }
      }
    }
  ]
}

// Computed
const isCustomField = computed(() => {
  return props.column.key.startsWith('customFields.') ||
         props.column.id.startsWith('custom_')
})

// 状态选项配置
const statusOptions = computed(() => [
  {
    value: ReviewItemStatus.PASS,
    label: '通过',
    type: 'success',
    icon: CircleCheck
  },
  {
    value: ReviewItemStatus.FAIL,
    label: '不通过',
    type: 'danger',
    icon: CircleClose
  },
  {
    value: ReviewItemStatus.SKIP,
    label: '跳过',
    type: 'warning',
    icon: Remove
  }
])

// 是否需要备注
const isCommentRequired = computed(() => {
  return pendingStatus.value === ReviewItemStatus.FAIL
})

// 移除这个计算属性，直接使用 props.readonly

// Methods

const startEdit = () => {
  if (!props.column.editable) return
  
  editing.value = true
  if (props.column.key === 'comment') {
    editValue.value = props.row.comment || ''
  } else if (isCustomField.value) {
    editValue.value = getCustomFieldValue()
  } else {
    editValue.value = getCellValue()
  }
}

const handleCommentSave = () => {
  editing.value = false
  if (editValue.value !== props.row.comment) {
    emit('cell-change', props.row.itemId, 'comment', editValue.value)
  }
}

const handleCustomFieldSave = () => {
  editing.value = false
  const fieldKey = props.column.key.startsWith('customFields.')
    ? props.column.key.replace('customFields.', '')
    : props.column.key

  const currentValue = getCustomFieldValue()
  if (editValue.value !== currentValue) {
    emit('cell-change', props.row.itemId, `customFields.${fieldKey}`, editValue.value)
  }
}

// 状态变更相关方法
const handleStatusChange = (status: ReviewItemStatus) => {
  if (statusChanging.value) return

  pendingStatus.value = status

  // 如果是不通过状态或当前已有备注，显示备注对话框
  if (status === ReviewItemStatus.FAIL || props.row.comment) {
    statusForm.value.comment = props.row.comment || ''
    showCommentDialog.value = true
  } else {
    // 直接变更状态
    confirmStatusChange()
  }
}

const confirmStatusChange = async () => {
  if (isCommentRequired.value && statusFormRef.value) {
    try {
      await statusFormRef.value.validate()
    } catch {
      return
    }
  }

  statusChanging.value = true

  try {
    emit('status-change', props.row.itemId, pendingStatus.value, statusForm.value.comment)

    showCommentDialog.value = false
    statusForm.value.comment = ''

    ElMessage.success(`状态已更新为：${getStatusText(pendingStatus.value)}`)
  } catch (error) {
    ElMessage.error('状态更新失败')
  } finally {
    statusChanging.value = false
  }
}

const cancelStatusChange = () => {
  showCommentDialog.value = false
  statusForm.value.comment = ''
  pendingStatus.value = ReviewItemStatus.PENDING
}

const editComment = () => {
  statusForm.value.comment = props.row.comment || ''
  pendingStatus.value = props.row.status
  showCommentDialog.value = true
}

const getCellValue = () => {
  return (props.row as any)[props.column.key]
}

const getCustomFieldValue = () => {
  const fieldKey = props.column.key.startsWith('customFields.') 
    ? props.column.key.replace('customFields.', '')
    : props.column.key
  
  return props.row.customFields?.[fieldKey]
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatCustomDate = (dateValue: any) => {
  if (!dateValue) return ''
  const date = new Date(dateValue)
  return date.toLocaleDateString('zh-CN')
}

// Status related methods
const getStatusText = (status: ReviewItemStatus) => {
  const statusMap = {
    [ReviewItemStatus.PENDING]: '待处理',
    [ReviewItemStatus.PASS]: '通过',
    [ReviewItemStatus.FAIL]: '不通过',
    [ReviewItemStatus.SKIP]: '跳过',
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: ReviewItemStatus) => {
  const typeMap = {
    [ReviewItemStatus.PENDING]: 'info',
    [ReviewItemStatus.PASS]: 'success',
    [ReviewItemStatus.FAIL]: 'danger',
    [ReviewItemStatus.SKIP]: 'warning',
  }
  return typeMap[status] || 'info'
}

const getStatusIcon = (status: ReviewItemStatus) => {
  const iconMap = {
    [ReviewItemStatus.PENDING]: 'Clock',
    [ReviewItemStatus.PASS]: 'CircleCheck',
    [ReviewItemStatus.FAIL]: 'CircleClose',
    [ReviewItemStatus.SKIP]: 'Remove',
  }
  return iconMap[status] || 'Clock'
}
</script>

<style scoped>
.table-cell {
  .sequence-cell {
    font-weight: 500;
    color: #606266;
  }

  .content-cell {
    .content-text {
      margin: 0 0 4px 0;
      line-height: 1.4;
      color: #303133;
    }

    .required-indicator {
      margin-top: 4px;
    }
  }

  .status-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;

    .status-selector {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .compact-status-buttons {
      display: flex;

      .status-btn {
        padding: 2px 6px;
        min-width: auto;
        font-size: 12px;
        height: 24px;

        .status-text {
          margin-left: 2px;
          font-size: 11px;
        }

        &.status-pass {
          &.el-button--success {
            background-color: #67c23a;
            border-color: #67c23a;
            color: white;
          }
        }

        &.status-fail {
          &.el-button--danger {
            background-color: #f56c6c;
            border-color: #f56c6c;
            color: white;
          }
        }

        &.status-skip {
          &.el-button--warning {
            background-color: #e6a23c;
            border-color: #e6a23c;
            color: white;
          }
        }
      }
    }

    .comment-btn {
      padding: 4px;
      min-width: auto;
      position: relative;

      .comment-indicator {
        position: absolute;
        top: -2px;
        right: -2px;
        color: #409eff;
        font-size: 12px;
        font-weight: bold;
      }
    }
  }

  .comment-cell {
    .comment-display {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      min-height: 20px;
    }

    .comment-text {
      color: #303133;
      line-height: 1.4;
    }

    .comment-placeholder {
      color: #c0c4cc;
      font-style: italic;
    }

    .edit-icon {
      opacity: 0;
      transition: opacity 0.3s;
      color: #909399;
      font-size: 12px;
    }

    .comment-display:hover .edit-icon {
      opacity: 1;
    }
  }

  .custom-field-cell {
    .text-value,
    .number-value,
    .date-value,
    .tag-placeholder {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      min-height: 20px;
    }

    .tag-placeholder {
      color: #c0c4cc;
    }

    .edit-icon {
      opacity: 0;
      transition: opacity 0.3s;
      color: #909399;
      font-size: 12px;
    }

    .text-value:hover .edit-icon,
    .number-value:hover .edit-icon,
    .date-value:hover .edit-icon,
    .tag-placeholder:hover .edit-icon {
      opacity: 1;
    }
  }

  .reviewer-cell,
  .time-cell,
  .default-cell {
    color: #606266;
  }

  .status-comment-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
