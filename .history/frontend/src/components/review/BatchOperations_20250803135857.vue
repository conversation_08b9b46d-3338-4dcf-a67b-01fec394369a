<template>
  <div class="batch-operations" :class="{ 'visible': hasSelectedItems }">
    <el-card class="batch-card" shadow="always">
      <div class="batch-header">
        <div class="selection-info">
          <el-icon><Select /></el-icon>
          <span>已选择 {{ selectedCount }} 项</span>
          <el-button type="text" size="small" @click="clearSelection">
            清除选择
          </el-button>
        </div>
        
        <div class="batch-actions">
          <el-button-group>
            <el-button
              v-for="button in batchButtons"
              :key="button.id"
              :type="button.type"
              @click="handleBatchAction(button)"
              :loading="processing"
              :disabled="!hasSelectedItems || !button.enabled"
            >
              <el-icon v-if="button.icon">
                <component :is="button.icon" />
              </el-icon>
              一键{{ button.label }}
            </el-button>
          </el-button-group>
          
          <el-dropdown @command="handleAdvancedAction" trigger="click">
            <el-button type="info">
              更多操作
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="selectAll">
                  <el-icon><Select /></el-icon>
                  全选
                </el-dropdown-item>
                <el-dropdown-item command="selectRequired">
                  <el-icon><Star /></el-icon>
                  选择必填项
                </el-dropdown-item>
                <el-dropdown-item command="selectByCategory" divided>
                  <el-icon><Collection /></el-icon>
                  按分类选择
                </el-dropdown-item>
                <el-dropdown-item command="selectPending">
                  <el-icon><Clock /></el-icon>
                  选择待处理项
                </el-dropdown-item>
                <el-dropdown-item command="invertSelection" divided>
                  <el-icon><Switch /></el-icon>
                  反选
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- Progress indicator for batch operations -->
      <div v-if="processing" class="batch-progress">
        <el-progress 
          :percentage="progressPercentage" 
          :status="progressStatus"
          :stroke-width="6"
        />
        <div class="progress-text">
          {{ progressText }}
        </div>
      </div>
    </el-card>

    <!-- Batch fail dialog -->
    <el-dialog
      v-model="batchFailDialogVisible"
      title="批量设置不通过"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="batch-fail-content">
        <div class="selected-items-preview">
          <h4>将要设置为不通过的项目 ({{ selectedCount }} 项):</h4>
          <div class="items-list">
            <div 
              v-for="item in selectedItemsPreview" 
              :key="item.itemId"
              class="preview-item"
            >
              <span class="item-sequence">{{ item.sequence }}</span>
              <span class="item-content">{{ item.content }}</span>
            </div>
          </div>
        </div>
        
        <div class="comment-input-section">
          <h4>不通过原因 <span class="required">*</span></h4>
          <el-input
            v-model="batchFailComment"
            type="textarea"
            placeholder="请输入批量不通过的原因..."
            :rows="4"
            :maxlength="500"
            show-word-limit
            :class="{ 'error': showCommentError }"
          />
          <div v-if="showCommentError" class="error-message">
            <el-icon><Warning /></el-icon>
            批量设置不通过时必须填写原因
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchFailDialogVisible = false">取消</el-button>
          <el-button 
            type="danger" 
            @click="confirmBatchFail"
            :loading="processing"
            :disabled="!batchFailComment.trim()"
          >
            确认设置不通过
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Category selection dialog -->
    <el-dialog
      v-model="categoryDialogVisible"
      title="按分类选择"
      width="400px"
    >
      <div class="category-selection">
        <el-checkbox-group v-model="selectedCategories">
          <div 
            v-for="category in availableCategories" 
            :key="category.name"
            class="category-option"
          >
            <el-checkbox :value="category.name">
              {{ category.name }} ({{ category.count }} 项)
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="categoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="selectByCategories">
            确认选择
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  Select, CircleCheck, CircleClose, Remove, ArrowDown, 
  Star, Collection, Clock, Switch, Warning 
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { type ReviewItem, ReviewItemStatus } from '@/api/review'
import { type StatusButtonConfig, DEFAULT_BUTTON_CONFIG } from '@/types/defect-config'

// Props
interface Props {
  selectedItems: string[]
  allItems: ReviewItem[]
  processing?: boolean
  buttonConfig?: StatusButtonConfig
}

const props = withDefaults(defineProps<Props>(), {
  processing: false,
  buttonConfig: () => DEFAULT_BUTTON_CONFIG
})

// Emits
interface Emits {
  (e: 'batchUpdate', itemIds: string[], status: ReviewItemStatus, comment?: string): void
  (e: 'updateSelection', itemIds: string[]): void
  (e: 'clearSelection'): void
}

const emit = defineEmits<Emits>()

// Reactive data
const processing = ref(false)
const progressPercentage = ref(0)
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const progressText = ref('')
const batchFailDialogVisible = ref(false)
const batchFailComment = ref('')
const showCommentError = ref(false)
const categoryDialogVisible = ref(false)
const selectedCategories = ref<string[]>([])

// Computed properties
const hasSelectedItems = computed(() => props.selectedItems.length > 0)

const selectedCount = computed(() => props.selectedItems.length)

const selectedItemsPreview = computed(() => {
  return props.allItems
    .filter(item => props.selectedItems.includes(item.itemId))
    .sort((a, b) => a.sequence - b.sequence)
    .slice(0, 10) // Show only first 10 items in preview
})

const availableCategories = computed(() => {
  const categoryMap = new Map<string, number>()

  props.allItems.forEach(item => {
    if (item.category) {
      categoryMap.set(item.category, (categoryMap.get(item.category) || 0) + 1)
    }
  })

  return Array.from(categoryMap.entries()).map(([name, count]) => ({
    name,
    count
  })).sort((a, b) => a.name.localeCompare(b.name))
})

// 批量操作按钮配置
const batchButtons = computed(() => {
  return props.buttonConfig.buttons.filter(button =>
    button.enabled && button.status
  )
})

// Methods
const clearSelection = () => {
  emit('clearSelection')
}

const batchPass = async () => {
  try {
    await ElMessageBox.confirm(
      `确认将选中的 ${selectedCount.value} 项设置为通过？`,
      '批量通过确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success',
      }
    )
    
    await performBatchOperation(ReviewItemStatus.PASS)
    
  } catch (err) {
    if (err !== 'cancel') {
      console.error('Batch pass failed:', err)
    }
  }
}

const showBatchFailDialog = () => {
  batchFailComment.value = ''
  showCommentError.value = false
  batchFailDialogVisible.value = true
}

const confirmBatchFail = async () => {
  if (!batchFailComment.value.trim()) {
    showCommentError.value = true
    return
  }
  
  try {
    batchFailDialogVisible.value = false
    await performBatchOperation(ReviewItemStatus.FAIL, batchFailComment.value)
    
  } catch (err) {
    console.error('Batch fail failed:', err)
  }
}

const batchSkip = async () => {
  try {
    await ElMessageBox.confirm(
      `确认将选中的 ${selectedCount.value} 项设置为跳过？`,
      '批量跳过确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await performBatchOperation(ReviewItemStatus.SKIP)
    
  } catch (err) {
    if (err !== 'cancel') {
      console.error('Batch skip failed:', err)
    }
  }
}

const performBatchOperation = async (status: ReviewItemStatus, comment?: string) => {
  try {
    processing.value = true
    progressPercentage.value = 0
    progressStatus.value = ''
    
    const total = props.selectedItems.length
    let completed = 0
    
    // Update progress text
    const statusText = {
      [ReviewItemStatus.PASS]: '通过',
      [ReviewItemStatus.FAIL]: '不通过',
      [ReviewItemStatus.SKIP]: '跳过',
      [ReviewItemStatus.PENDING]: '待处理'
    }[status]
    
    progressText.value = `正在批量设置为${statusText}... (${completed}/${total})`
    
    // Simulate progress updates
    const progressInterval = setInterval(() => {
      if (completed < total) {
        completed++
        progressPercentage.value = Math.round((completed / total) * 100)
        progressText.value = `正在批量设置为${statusText}... (${completed}/${total})`
      } else {
        clearInterval(progressInterval)
      }
    }, 100)
    
    // Perform the actual batch update
    emit('batchUpdate', props.selectedItems, status, comment)
    
    // Wait for completion
    await new Promise(resolve => setTimeout(resolve, total * 100 + 500))
    
    progressStatus.value = 'success'
    progressText.value = `批量操作完成！成功处理 ${total} 项`
    
    ElMessage.success(`成功将 ${total} 项设置为${statusText}`)
    
    // Clear selection after successful operation
    setTimeout(() => {
      emit('clearSelection')
    }, 1000)
    
  } catch (err: any) {
    progressStatus.value = 'exception'
    progressText.value = '批量操作失败'
    ElMessage.error(err.message || '批量操作失败')
    throw err
  } finally {
    setTimeout(() => {
      processing.value = false
      progressPercentage.value = 0
      progressStatus.value = ''
      progressText.value = ''
    }, 2000)
  }
}

const handleAdvancedAction = (command: string) => {
  switch (command) {
    case 'selectAll':
      selectAll()
      break
    case 'selectRequired':
      selectRequired()
      break
    case 'selectByCategory':
      showCategoryDialog()
      break
    case 'selectPending':
      selectPending()
      break
    case 'invertSelection':
      invertSelection()
      break
  }
}

const selectAll = () => {
  const allItemIds = props.allItems.map(item => item.itemId)
  emit('updateSelection', allItemIds)
  ElMessage.success(`已选择全部 ${allItemIds.length} 项`)
}

const selectRequired = () => {
  const requiredItemIds = props.allItems
    .filter(item => item.required)
    .map(item => item.itemId)
  
  if (requiredItemIds.length === 0) {
    ElMessage.info('没有必填项')
    return
  }
  
  emit('updateSelection', requiredItemIds)
  ElMessage.success(`已选择 ${requiredItemIds.length} 个必填项`)
}

const showCategoryDialog = () => {
  if (availableCategories.value.length === 0) {
    ElMessage.info('没有可用的分类')
    return
  }
  
  selectedCategories.value = []
  categoryDialogVisible.value = true
}

const selectByCategories = () => {
  if (selectedCategories.value.length === 0) {
    ElMessage.warning('请选择至少一个分类')
    return
  }
  
  const categoryItemIds = props.allItems
    .filter(item => item.category && selectedCategories.value.includes(item.category))
    .map(item => item.itemId)
  
  emit('updateSelection', categoryItemIds)
  categoryDialogVisible.value = false
  
  ElMessage.success(`已选择 ${categoryItemIds.length} 项 (${selectedCategories.value.join(', ')})`)
}

const selectPending = () => {
  const pendingItemIds = props.allItems
    .filter(item => item.status === ReviewItemStatus.PENDING)
    .map(item => item.itemId)
  
  if (pendingItemIds.length === 0) {
    ElMessage.info('没有待处理项')
    return
  }
  
  emit('updateSelection', pendingItemIds)
  ElMessage.success(`已选择 ${pendingItemIds.length} 个待处理项`)
}

const invertSelection = () => {
  const currentSelected = new Set(props.selectedItems)
  const invertedSelection = props.allItems
    .filter(item => !currentSelected.has(item.itemId))
    .map(item => item.itemId)
  
  emit('updateSelection', invertedSelection)
  ElMessage.success(`已反选，当前选择 ${invertedSelection.length} 项`)
}

// Watch for comment changes
watch(() => batchFailComment.value, () => {
  if (showCommentError.value && batchFailComment.value.trim()) {
    showCommentError.value = false
  }
})
</script>

<style scoped>
.batch-operations {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
}

.batch-operations.visible {
  opacity: 1;
  visibility: visible;
}

.batch-card {
  min-width: 600px;
  max-width: 90vw;
}

.batch-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #409eff;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.batch-progress {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.progress-text {
  margin-top: 8px;
  text-align: center;
  font-size: 14px;
  color: #606266;
}

.batch-fail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.selected-items-preview h4,
.comment-input-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.required {
  color: #f56c6c;
}

.items-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #f5f7fa;
}

.preview-item:last-child {
  border-bottom: none;
}

.item-sequence {
  font-weight: bold;
  color: #409eff;
  min-width: 40px;
}

.item-content {
  flex: 1;
  color: #303133;
  font-size: 14px;
}

.comment-input-section .el-textarea.error :deep(.el-textarea__inner) {
  border-color: #f56c6c;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 12px;
  color: #f56c6c;
}

.category-selection {
  max-height: 300px;
  overflow-y: auto;
}

.category-option {
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
}

.category-option:last-child {
  border-bottom: none;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Responsive design */
@media (max-width: 768px) {
  .batch-operations {
    left: 10px;
    right: 10px;
    transform: none;
  }
  
  .batch-card {
    min-width: auto;
    width: 100%;
  }
  
  .batch-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .batch-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .batch-actions .el-button-group {
    flex: 1;
  }
  
  .batch-actions .el-button-group .el-button {
    flex: 1;
    font-size: 12px;
    padding: 8px 4px;
  }
}

/* Animation */
.batch-operations {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Button styling */
.batch-actions .el-button-group .el-button {
  border-radius: 0;
}

.batch-actions .el-button-group .el-button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.batch-actions .el-button-group .el-button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
</style>