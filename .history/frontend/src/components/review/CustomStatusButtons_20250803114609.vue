<template>
  <div class="custom-status-buttons">
    <div 
      :class="['button-group', `layout-${buttonConfig.layout}`]"
      v-if="visibleButtons.length > 0"
    >
      <el-button
        v-for="button in visibleButtons"
        :key="button.id"
        :type="button.type"
        :loading="loadingButtons.has(button.id)"
        :disabled="!button.enabled || readonly"
        @click="handleButtonClick(button)"
        class="status-button"
        :class="`status-${button.id}`"
      >
        <el-icon v-if="button.icon">
          <component :is="button.icon" />
        </el-icon>
        {{ button.label }}
      </el-button>
    </div>

    <!-- 备注输入对话框 -->
    <el-dialog
      v-model="showCommentDialog"
      :title="`${currentButton?.label} - 添加备注`"
      width="500px"
      :before-close="cancelComment"
    >
      <div class="comment-form">
        <el-form :model="commentForm" :rules="commentRules" ref="commentFormRef">
          <el-form-item 
            label="备注内容" 
            prop="comment"
            :required="currentButton?.action.requireComment"
          >
            <el-input
              v-model="commentForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入备注内容..."
              :maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <!-- 自定义字段输入 -->
          <div v-if="customFieldInputs.length > 0" class="custom-fields">
            <h4>附加信息</h4>
            <el-form-item
              v-for="field in customFieldInputs"
              :key="field.key"
              :label="field.label"
              :prop="`customFields.${field.key}`"
            >
              <el-input
                v-if="field.type === 'text'"
                v-model="commentForm.customFields[field.key]"
                :placeholder="`请输入${field.label}`"
              />
              <el-input-number
                v-else-if="field.type === 'number'"
                v-model="commentForm.customFields[field.key]"
                :placeholder="`请输入${field.label}`"
                style="width: 100%"
              />
              <el-select
                v-else-if="field.type === 'select'"
                v-model="commentForm.customFields[field.key]"
                :placeholder="`请选择${field.label}`"
                style="width: 100%"
              >
                <el-option
                  v-for="option in (field.options || [])"
                  :key="option"
                  :label="option"
                  :value="option"
                />
              </el-select>
              <el-date-picker
                v-else-if="field.type === 'date'"
                v-model="commentForm.customFields[field.key]"
                type="date"
                :placeholder="`请选择${field.label}`"
                style="width: 100%"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelComment">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmComment"
            :loading="submitting"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 缺陷生成确认对话框 -->
    <el-dialog
      v-model="showDefectDialog"
      title="生成缺陷确认"
      width="600px"
    >
      <div class="defect-preview">
        <h4>即将生成的缺陷信息：</h4>
        <div class="defect-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="缺陷标题">
              {{ pendingDefect?.title }}
            </el-descriptions-item>
            <el-descriptions-item label="缺陷描述">
              <div class="defect-description">{{ pendingDefect?.description }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="严重程度">
              <el-tag :type="getSeverityTagType(pendingDefect?.severity)">
                {{ pendingDefect?.severity }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="缺陷分类">
              {{ pendingDefect?.category }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDefectGeneration">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmDefectGeneration"
            :loading="generatingDefect"
          >
            确认生成
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  type CustomStatusButton,
  type StatusButtonConfig,
  type GeneratedDefect,
  type DefectRule,
  DEFAULT_BUTTON_CONFIG,
  replaceTemplateVariables,
  checkConditions
} from '@/types/defect-config'
import { type ExtendedReviewItem } from '@/types/table-config'
import { ReviewItemStatus } from '@/api/review'

// Props
interface Props {
  item: ExtendedReviewItem
  buttonConfig?: StatusButtonConfig
  defectRules?: DefectRule[]
  readonly?: boolean
  currentUser?: string
}

const props = withDefaults(defineProps<Props>(), {
  buttonConfig: () => DEFAULT_BUTTON_CONFIG,
  defectRules: () => [],
  readonly: false,
  currentUser: 'current-user'
})

// Emits
interface Emits {
  (e: 'status-change', button: CustomStatusButton, payload: any): void
  (e: 'defect-generated', defect: GeneratedDefect): void
}

const emit = defineEmits<Emits>()

// Reactive data
const loadingButtons = ref(new Set<string>())
const showCommentDialog = ref(false)
const showDefectDialog = ref(false)
const submitting = ref(false)
const generatingDefect = ref(false)
const currentButton = ref<CustomStatusButton | null>(null)
const pendingDefect = ref<GeneratedDefect | null>(null)

const commentForm = ref({
  comment: '',
  customFields: {} as Record<string, any>
})

const commentFormRef = ref()

// Validation rules
const commentRules = computed(() => ({
  comment: currentButton.value?.action.requireComment 
    ? [{ required: true, message: '请输入备注内容', trigger: 'blur' }]
    : []
}))

// Computed
const visibleButtons = computed(() => {
  return props.buttonGroup.buttons
    .filter(button => {
      if (!button.enabled) return false
      
      // 检查显示条件
      if (button.displayConditions) {
        const { currentStatus, customFields, userRoles } = button.displayConditions
        
        // 检查当前状态条件
        if (currentStatus && !currentStatus.includes(props.item.status)) {
          return false
        }
        
        // 检查自定义字段条件
        if (customFields) {
          for (const [field, value] of Object.entries(customFields)) {
            if (props.item.customFields?.[field] !== value) {
              return false
            }
          }
        }
        
        // 这里可以添加用户角色检查逻辑
        // if (userRoles && !userRoles.includes(currentUserRole)) return false
      }
      
      return true
    })
    .sort((a, b) => a.order - b.order)
})

const customFieldInputs = computed(() => {
  if (!currentButton.value) return []
  
  // 根据按钮配置返回需要输入的自定义字段
  const button = currentButton.value
  const inputs = []
  
  // 检查payload模板中是否需要自定义字段
  const payloadStr = JSON.stringify(button.action.payloadTemplate)
  
  if (payloadStr.includes('${customFields.conditions}')) {
    inputs.push({
      key: 'conditions',
      label: '通过条件',
      type: 'text',
      required: true
    })
  }
  
  if (payloadStr.includes('${customFields.partialDetails}')) {
    inputs.push({
      key: 'partialDetails',
      label: '部分通过详情',
      type: 'text',
      required: true
    })
  }
  
  if (payloadStr.includes('${customFields.assignee}')) {
    inputs.push({
      key: 'assignee',
      label: '指派人员',
      type: 'text',
      required: false,
      options: ['高', '中', '低']
    })
  }
  
  return inputs
})

// Methods
const handleButtonClick = async (button: CustomStatusButton) => {
  currentButton.value = button
  
  // 显示确认对话框
  if (button.action.confirmMessage) {
    try {
      await ElMessageBox.confirm(
        button.action.confirmMessage,
        '确认操作',
        { type: 'warning' }
      )
    } catch {
      return // 用户取消
    }
  }
  
  // 如果需要备注或自定义字段，显示输入对话框
  if (button.action.requireComment || customFieldInputs.value.length > 0) {
    showCommentDialog.value = true
    commentForm.value = {
      comment: '',
      customFields: {}
    }
    return
  }
  
  // 直接执行操作
  await executeButtonAction(button, {})
}

const confirmComment = async () => {
  try {
    await commentFormRef.value.validate()
    
    const payload = {
      comment: commentForm.value.comment,
      customFields: commentForm.value.customFields
    }
    
    showCommentDialog.value = false
    await executeButtonAction(currentButton.value!, payload)
  } catch {
    // 验证失败
  }
}

const cancelComment = () => {
  showCommentDialog.value = false
  currentButton.value = null
  commentForm.value = {
    comment: '',
    customFields: {}
  }
}

const executeButtonAction = async (button: CustomStatusButton, formData: any) => {
  try {
    loadingButtons.value.add(button.id)
    submitting.value = true

    // 构建请求载荷
    const context = {
      currentUser: props.currentUser,
      currentTime: new Date().toISOString(),
      comment: formData.comment || '',
      customFields: formData.customFields || {}
    }

    const payload = buildPayload(button.action.payloadTemplate, context)

    // 检查是否为本地更新模式
    if (payload.updateLocal && payload.deferSubmit) {
      // 本地更新模式：只更新本地状态，不发送请求
      emit('status-change', button, payload)

      // 显示成功消息
      if (button.action.successMessage) {
        ElMessage.success(button.action.successMessage)
      }
    } else {
      // 传统模式：发送状态变更事件（由父组件处理API调用）
      emit('status-change', button, payload)

      // 执行后续动作
      if (button.action.postActions) {
        for (const postAction of button.action.postActions) {
          await executePostAction(postAction, context)
        }
      }

      // 显示成功消息
      if (button.action.successMessage) {
        ElMessage.success(button.action.successMessage)
      }
    }

  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    loadingButtons.value.delete(button.id)
    submitting.value = false
    currentButton.value = null
  }
}

const buildPayload = (template: Record<string, any>, context: Record<string, any>) => {
  const payload = { ...template }
  
  // 替换模板变量
  const templateStr = JSON.stringify(payload)
  const replacedStr = replaceTemplateVariables(templateStr, props.item, context)
  
  return JSON.parse(replacedStr)
}

const executePostAction = async (postAction: any, context: Record<string, any>) => {
  switch (postAction.type) {
    case 'generateDefect':
      await handleDefectGeneration(postAction.config, context)
      break
    case 'sendNotification':
      // 实现通知发送逻辑
      console.log('Send notification:', postAction.config)
      break
    case 'updateFields':
      // 实现字段更新逻辑
      console.log('Update fields:', postAction.config)
      break
    case 'callWebhook':
      // 实现webhook调用逻辑
      console.log('Call webhook:', postAction.config)
      break
  }
}

const handleDefectGeneration = async (config: any, context: Record<string, any>) => {
  const ruleId = config.ruleId
  const rule = props.defectRules.find(r => r.id === ruleId)
  
  if (!rule || !rule.enabled) return
  
  // 检查触发条件
  if (!rule.trigger.status.includes(props.item.status as ReviewItemStatus)) return
  
  if (rule.trigger.conditions && !checkConditions(rule.trigger.conditions, props.item)) {
    return
  }
  
  // 生成缺陷信息
  const defect = generateDefect(rule, context)
  
  if (rule.options.requireConfirmation && !config.autoConfirm) {
    // 显示确认对话框
    pendingDefect.value = defect
    showDefectDialog.value = true
  } else {
    // 直接生成
    emit('defect-generated', defect)
  }
}

const generateDefect = (rule: DefectRule, context: Record<string, any>): GeneratedDefect => {
  const template = rule.template
  
  // 生成标题和描述
  const title = replaceTemplateVariables(template.titleTemplate, props.item, context)
  const description = replaceTemplateVariables(template.descriptionTemplate, props.item, context)
  
  // 确定严重程度
  const severity = template.severityMapping[props.item.category] || template.severityMapping.default || 'medium'
  
  // 确定分类
  const category = template.categoryMapping[props.item.category] || template.categoryMapping.default || 'general'
  
  // 构建自定义数据
  const customData: Record<string, any> = {}
  Object.entries(template.customFieldMapping).forEach(([sourceField, targetField]) => {
    const value = props.item.customFields?.[sourceField]
    if (value !== undefined) {
      customData[targetField] = value
    }
  })
  
  return {
    id: `defect_${Date.now()}`,
    title,
    description,
    severity,
    category,
    sourceItemId: props.item.itemId,
    sourceContent: props.item.content,
    customData,
    createdTime: new Date().toISOString(),
    status: 'draft'
  }
}

const confirmDefectGeneration = () => {
  if (pendingDefect.value) {
    emit('defect-generated', pendingDefect.value)
    showDefectDialog.value = false
    pendingDefect.value = null
  }
}

const cancelDefectGeneration = () => {
  showDefectDialog.value = false
  pendingDefect.value = null
}

const getSeverityTagType = (severity?: string) => {
  const typeMap: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[severity || 'medium'] || 'info'
}
</script>

<style scoped>
.custom-status-buttons {
  .button-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .button-group.layout-horizontal {
    flex-direction: row;
  }

  .button-group.layout-vertical {
    flex-direction: column;
    align-items: flex-start;
  }

  .button-group.layout-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
  }

  .status-button {
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 80px;
    justify-content: center;
  }

  .comment-form {
    .custom-fields {
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid #e4e7ed;
    }

    .custom-fields h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 14px;
    }
  }

  .defect-preview {
    .defect-info {
      margin-top: 16px;
    }

    .defect-description {
      white-space: pre-wrap;
      line-height: 1.6;
      max-height: 200px;
      overflow-y: auto;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-status-buttons {
    .button-group {
      flex-direction: column;
      align-items: stretch;
    }

    .status-button {
      width: 100%;
      min-width: auto;
    }
  }
}
</style>
