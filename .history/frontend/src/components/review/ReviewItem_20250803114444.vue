<template>
  <div 
    class="review-item"
    :class="{ 
      'selected': selected,
      'required': item.required,
      'status-pass': item.status === 'PASS',
      'status-fail': item.status === 'FAIL',
      'status-skip': item.status === 'SKIP',
      'status-pending': item.status === 'PENDING'
    }"
  >
    <!-- Item header with selection and basic info -->
    <div class="item-header">
      <el-checkbox 
        :model-value="selected"
        @update:model-value="$emit('select', item.itemId, $event)"
        class="item-checkbox"
      />
      
      <div class="item-sequence">
        <el-badge :value="item.sequence" type="primary" />
      </div>
      
      <div class="item-content">
        <p class="content-text">{{ item.content }}</p>
        <div class="item-meta">
          <el-tag v-if="item.required" type="danger" size="small" effect="plain">
            <el-icon><Star /></el-icon>
            必填
          </el-tag>
          <el-tag v-if="item.category" type="info" size="small" effect="plain">
            {{ item.category }}
          </el-tag>
          <span v-if="item.reviewer" class="reviewer-info">
            评审人: {{ item.reviewer }}
          </span>
          <span v-if="item.reviewTime" class="review-time">
            {{ formatTime(item.reviewTime) }}
          </span>
        </div>
      </div>
      
      <div class="item-status">
        <el-tag 
          :type="getStatusTagType(item.status)" 
          :effect="item.status === 'PENDING' ? 'plain' : 'dark'"
          size="large"
        >
          <el-icon>
            <component :is="getStatusIcon(item.status)" />
          </el-icon>
          {{ getStatusText(item.status) }}
        </el-tag>
      </div>
    </div>

    <!-- Status selection controls -->
    <div class="item-actions">
      <!-- 使用自定义状态按钮组件 -->
      <CustomStatusButtons
        :item="item"
        :button-config="buttonConfig"
        :defect-rules="defectRules"
        :readonly="readonly"
        :current-user="currentUser"
        @status-change="handleCustomStatusChange"
        @defect-generated="handleDefectGenerated"
      />

      <!-- 传统状态控制（可选显示） -->
      <div v-if="showTraditionalControls" class="traditional-controls">
        <el-radio-group
          :model-value="item.status"
          @update:model-value="onStatusChange"
          class="status-radio-group"
          size="large"
        >
          <el-radio-button value="PASS" class="status-pass-btn">
            <el-icon><CircleCheck /></el-icon>
            通过
          </el-radio-button>
          <el-radio-button value="FAIL" class="status-fail-btn">
            <el-icon><CircleClose /></el-icon>
            不通过
          </el-radio-button>
          <el-radio-button value="SKIP" class="status-skip-btn">
            <el-icon><Remove /></el-icon>
            跳过
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- Comment input for FAIL status -->
    <div v-if="item.status === 'FAIL'" class="item-comment">
      <div class="comment-header">
        <el-icon><EditPen /></el-icon>
        <span>不通过原因</span>
        <span class="required-mark">*</span>
      </div>
      <el-input
        :model-value="item.comment"
        @update:model-value="onCommentChange"
        @blur="onCommentBlur"
        type="textarea"
        placeholder="请详细说明不通过的原因..."
        :rows="3"
        :maxlength="500"
        show-word-limit
        class="comment-input"
        :class="{ 'error': showCommentError }"
      />
      <div v-if="showCommentError" class="comment-error">
        <el-icon><Warning /></el-icon>
        不通过时必须填写原因
      </div>
    </div>

    <!-- Review history -->
    <div v-if="item.reviewHistory && item.reviewHistory.length > 0" class="item-history">
      <div class="history-header">
        <el-button 
          type="text" 
          size="small" 
          @click="toggleHistory"
          class="history-toggle"
        >
          <el-icon>
            <component :is="showHistory ? 'ArrowUp' : 'ArrowDown'" />
          </el-icon>
          评审历史 ({{ item.reviewHistory.length }})
        </el-button>
        
        <div v-if="hasConflicts" class="conflict-indicator">
          <el-icon color="#f56c6c"><Warning /></el-icon>
          <span>存在冲突</span>
        </div>
      </div>
      
      <el-collapse-transition>
        <div v-show="showHistory" class="history-content">
          <el-timeline>
            <el-timeline-item
              v-for="(history, index) in sortedHistory"
              :key="history.id"
              :timestamp="formatTime(history.reviewTime)"
              :type="getHistoryTimelineType(history.status)"
              :icon="getStatusIcon(history.status)"
            >
              <div class="history-item">
                <div class="history-header-info">
                  <span class="history-reviewer">{{ history.reviewer }}</span>
                  <el-tag 
                    :type="getStatusTagType(history.status)" 
                    size="small"
                  >
                    {{ getStatusText(history.status) }}
                  </el-tag>
                  <span class="history-action">{{ history.action }}</span>
                </div>
                <div v-if="history.comment" class="history-comment">
                  <el-icon><ChatDotRound /></el-icon>
                  {{ history.comment }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-collapse-transition>
    </div>

    <!-- Loading overlay -->
    <div v-if="updating" class="loading-overlay">
      <el-icon class="is-loading"><Loading /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  Star, CircleCheck, CircleClose, Remove, Check, Close, 
  EditPen, Warning, ArrowUp, ArrowDown, ChatDotRound, Loading 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { type ReviewItem, type ReviewHistory, ReviewItemStatus } from '@/api/review'
import {
  type StatusButtonConfig,
  type DefectRule,
  type GeneratedDefect,
  type CustomStatusButton,
  DEFAULT_BUTTON_CONFIG
} from '@/types/defect-config'
import CustomStatusButtons from './CustomStatusButtons.vue'

// Props
interface Props {
  item: ReviewItem
  selected?: boolean
  readonly?: boolean
  buttonGroup?: StatusButtonGroup
  defectRules?: DefectRule[]
  currentUser?: string
  showTraditionalControls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  readonly: false,
  buttonGroup: () => DEFAULT_BUTTON_GROUP,
  defectRules: () => [],
  currentUser: 'current-user',
  showTraditionalControls: false
})

// Emits
interface Emits {
  (e: 'select', itemId: string, selected: boolean): void
  (e: 'statusChange', item: ReviewItem, status: ReviewItemStatus): void
  (e: 'commentChange', item: ReviewItem, comment: string): void
  (e: 'update', item: ReviewItem): void
  (e: 'customStatusChange', button: CustomStatusButton, payload: any): void
  (e: 'defectGenerated', defect: GeneratedDefect): void
}

const emit = defineEmits<Emits>()

// Reactive data
const updating = ref(false)
const showHistory = ref(false)
const showCommentError = ref(false)
const commentValue = ref(props.item.comment || '')

// Computed properties
const sortedHistory = computed(() => {
  if (!props.item.reviewHistory) return []
  return [...props.item.reviewHistory].sort((a, b) => 
    new Date(b.reviewTime).getTime() - new Date(a.reviewTime).getTime()
  )
})

const hasConflicts = computed(() => {
  if (!props.item.reviewHistory || props.item.reviewHistory.length < 2) return false
  
  const statuses = new Set(props.item.reviewHistory.map(h => h.status))
  return statuses.size > 1
})

// Methods
const onStatusChange = async (status: ReviewItemStatus) => {
  if (props.readonly || updating.value) return
  
  try {
    updating.value = true
    
    // Clear comment error when changing status
    showCommentError.value = false
    
    // If changing to FAIL, ensure comment is provided
    if (status === ReviewItemStatus.FAIL && !commentValue.value.trim()) {
      showCommentError.value = true
      ElMessage.warning('不通过时必须填写原因')
      return
    }
    
    // If changing from FAIL to other status, clear comment
    if (status !== ReviewItemStatus.FAIL) {
      commentValue.value = ''
    }
    
    emit('statusChange', props.item, status)
    
  } catch (error) {
    console.error('Failed to update status:', error)
  } finally {
    updating.value = false
  }
}

const onCommentChange = (value: string) => {
  commentValue.value = value
  showCommentError.value = false
}

const onCommentBlur = () => {
  if (props.item.status === ReviewItemStatus.FAIL && !commentValue.value.trim()) {
    showCommentError.value = true
    return
  }
  
  if (commentValue.value !== props.item.comment) {
    emit('commentChange', props.item, commentValue.value)
  }
}

const quickPass = () => {
  onStatusChange(ReviewItemStatus.PASS)
}

const quickFail = () => {
  if (!commentValue.value.trim()) {
    showCommentError.value = true
    ElMessage.warning('不通过时必须填写原因')
    return
  }
  onStatusChange(ReviewItemStatus.FAIL)
}

const toggleHistory = () => {
  showHistory.value = !showHistory.value
}

// 处理自定义状态按钮事件
const handleCustomStatusChange = (button: CustomStatusButton, payload: any) => {
  emit('customStatusChange', button, payload)
}

const handleDefectGenerated = (defect: GeneratedDefect) => {
  emit('defectGenerated', defect)
}

// Utility methods
const getStatusText = (status: ReviewItemStatus) => {
  const statusMap = {
    [ReviewItemStatus.PENDING]: '待处理',
    [ReviewItemStatus.PASS]: '通过',
    [ReviewItemStatus.FAIL]: '不通过',
    [ReviewItemStatus.SKIP]: '跳过',
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: ReviewItemStatus) => {
  const typeMap = {
    [ReviewItemStatus.PENDING]: 'info',
    [ReviewItemStatus.PASS]: 'success',
    [ReviewItemStatus.FAIL]: 'danger',
    [ReviewItemStatus.SKIP]: 'warning',
  }
  return typeMap[status] || 'info'
}

const getStatusIcon = (status: ReviewItemStatus) => {
  const iconMap = {
    [ReviewItemStatus.PENDING]: 'Clock',
    [ReviewItemStatus.PASS]: 'CircleCheck',
    [ReviewItemStatus.FAIL]: 'CircleClose',
    [ReviewItemStatus.SKIP]: 'Remove',
  }
  return iconMap[status] || 'Clock'
}

const getHistoryTimelineType = (status: ReviewItemStatus) => {
  const typeMap = {
    [ReviewItemStatus.PENDING]: 'info',
    [ReviewItemStatus.PASS]: 'success',
    [ReviewItemStatus.FAIL]: 'danger',
    [ReviewItemStatus.SKIP]: 'warning',
  }
  return typeMap[status] || 'info'
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Watch for prop changes
watch(() => props.item.comment, (newComment) => {
  commentValue.value = newComment || ''
})

watch(() => props.item.status, (newStatus) => {
  if (newStatus !== ReviewItemStatus.FAIL) {
    showCommentError.value = false
  }
})
</script>

<style scoped>
.review-item {
  position: relative;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  background-color: white;
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.review-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.review-item.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.review-item.required {
  border-left: 4px solid #f56c6c;
}

.review-item.status-pass {
  border-left: 4px solid #67c23a;
}

.review-item.status-fail {
  border-left: 4px solid #f56c6c;
}

.review-item.status-skip {
  border-left: 4px solid #e6a23c;
}

.item-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.item-checkbox {
  margin-top: 4px;
}

.item-sequence {
  flex-shrink: 0;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.content-text {
  margin: 0 0 8px 0;
  font-size: 16px;
  line-height: 1.6;
  color: #303133;
  word-wrap: break-word;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.reviewer-info,
.review-time {
  font-size: 12px;
  color: #909399;
}

.item-status {
  flex-shrink: 0;
}

.item-actions {
  margin-bottom: 16px;
}

.status-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.status-radio-group {
  flex: 1;
}

.status-radio-group :deep(.el-radio-button__inner) {
  padding: 12px 20px;
  font-weight: 500;
}

.status-pass-btn :deep(.el-radio-button__inner) {
  border-color: #67c23a;
  color: #67c23a;
}

.status-pass-btn :deep(.el-radio-button__inner:hover) {
  background-color: #f0f9ff;
}

.status-pass-btn.is-active :deep(.el-radio-button__inner) {
  background-color: #67c23a;
  border-color: #67c23a;
  color: white;
}

.status-fail-btn :deep(.el-radio-button__inner) {
  border-color: #f56c6c;
  color: #f56c6c;
}

.status-fail-btn :deep(.el-radio-button__inner:hover) {
  background-color: #fef0f0;
}

.status-fail-btn.is-active :deep(.el-radio-button__inner) {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.status-skip-btn :deep(.el-radio-button__inner) {
  border-color: #e6a23c;
  color: #e6a23c;
}

.status-skip-btn :deep(.el-radio-button__inner:hover) {
  background-color: #fdf6ec;
}

.status-skip-btn.is-active :deep(.el-radio-button__inner) {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

.quick-actions {
  display: flex;
  gap: 8px;
}

.item-comment {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fef0f0;
  border-radius: 8px;
  border: 1px solid #fbc4c4;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: #f56c6c;
}

.required-mark {
  color: #f56c6c;
  font-weight: bold;
}

.comment-input.error :deep(.el-textarea__inner) {
  border-color: #f56c6c;
}

.comment-error {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 12px;
  color: #f56c6c;
}

.item-history {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.history-toggle {
  padding: 0;
  font-size: 14px;
}

.conflict-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #f56c6c;
}

.history-content {
  padding-left: 16px;
}

.history-item {
  padding-bottom: 8px;
}

.history-header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.history-reviewer {
  font-weight: 500;
  color: #303133;
}

.history-action {
  font-size: 12px;
  color: #909399;
}

.history-comment {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  font-size: 14px;
  color: #606266;
  font-style: italic;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 4px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  z-index: 10;
}

.loading-overlay .el-icon {
  font-size: 24px;
  color: #409eff;
}

/* Responsive design */
@media (max-width: 768px) {
  .review-item {
    padding: 16px;
  }
  
  .item-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .item-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .status-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .status-radio-group :deep(.el-radio-button) {
    flex: 1;
  }
  
  .status-radio-group :deep(.el-radio-button__inner) {
    padding: 8px 12px;
    width: 100%;
    text-align: center;
  }
  
  .quick-actions {
    justify-content: center;
  }
  
  .history-header-info {
    flex-wrap: wrap;
  }
}

/* Animation */
.review-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>