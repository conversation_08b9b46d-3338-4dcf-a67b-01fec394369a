import { ReviewItemStatus } from '@/api/review'
import { type ExtendedReviewItem } from './table-config'

// 缺陷生成规则配置
export interface DefectRule {
  id: string
  name: string
  description: string
  enabled: boolean
  
  // 触发条件
  trigger: {
    status: ReviewItemStatus[]  // 触发的状态
    conditions?: DefectCondition[]  // 额外条件
  }
  
  // 缺陷模板
  template: {
    titleTemplate: string  // 缺陷标题模板，支持变量替换
    descriptionTemplate: string  // 缺陷描述模板
    severityMapping: Record<string, string>  // 严重程度映射
    categoryMapping: Record<string, string>  // 分类映射
    customFieldMapping: Record<string, string>  // 自定义字段映射
  }
  
  // 生成选项
  options: {
    autoGenerate: boolean  // 是否自动生成
    requireConfirmation: boolean  // 是否需要确认
    batchGenerate: boolean  // 是否支持批量生成
  }
}

// 缺陷条件
export interface DefectCondition {
  field: string  // 字段名
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'exists' | 'notExists'
  value: any
  logicOperator?: 'AND' | 'OR'
}

// 生成的缺陷信息
export interface GeneratedDefect {
  id: string
  title: string
  description: string
  severity: string
  category: string
  sourceItemId: string
  sourceContent: string
  customData: Record<string, any>
  createdTime: string
  status: 'draft' | 'confirmed' | 'submitted'
}

// 自定义状态按钮配置
export interface CustomStatusButton {
  id: string
  label: string
  status: ReviewItemStatus | string  // 支持自定义状态
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  enabled: boolean
  order: number
  
  // 按钮行为配置
  action: {
    method: 'POST' | 'PUT' | 'PATCH'
    requireComment: boolean  // 是否需要备注
    confirmMessage?: string  // 确认消息
    successMessage?: string  // 成功消息

    // 请求参数模板
    payloadTemplate: Record<string, any>

    // 后续动作
    postActions?: PostAction[]
  }
  
  // 显示条件
  displayConditions?: {
    currentStatus?: ReviewItemStatus[]  // 当前状态条件
    customFields?: Record<string, any>  // 自定义字段条件
    userRoles?: string[]  // 用户角色条件
  }
}

// 后续动作
export interface PostAction {
  type: 'generateDefect' | 'sendNotification' | 'updateFields' | 'callWebhook'
  config: Record<string, any>
}

// 状态按钮组配置
export interface StatusButtonGroup {
  id: string
  name: string
  description: string
  buttons: CustomStatusButton[]
  defaultButtons: string[]  // 默认显示的按钮ID
  layout: 'horizontal' | 'vertical' | 'grid'
}

// 默认缺陷生成规则
export const DEFAULT_DEFECT_RULES: DefectRule[] = [
  {
    id: 'fail-auto-defect',
    name: '不通过自动生成缺陷',
    description: '当检查项状态为不通过时，自动生成对应缺陷',
    enabled: true,
    trigger: {
      status: [ReviewItemStatus.FAIL]
    },
    template: {
      titleTemplate: '【${category}】${content}',
      descriptionTemplate: '检查项不通过详情：\n检查内容：${content}\n不通过原因：${comment}\n检查分类：${category}\n检查序号：${sequence}\n评审时间：${reviewTime}\n评审人员：${reviewer}\n\n${customFields.defectDescription || \'\'}',
      severityMapping: {
        '安全': 'high',
        '功能': 'medium',
        '性能': 'medium',
        '界面': 'low',
        'default': 'medium'
      },
      categoryMapping: {
        '安全检查': 'security',
        '功能检查': 'functional',
        '性能检查': 'performance',
        '界面检查': 'ui',
        'default': 'general'
      },
      customFieldMapping: {
        'defectType': 'type',
        'priority': 'priority',
        'assignee': 'assignedTo'
      }
    },
    options: {
      autoGenerate: false,
      requireConfirmation: true,
      batchGenerate: true
    }
  }
]

// 默认自定义状态按钮
export const DEFAULT_STATUS_BUTTONS: CustomStatusButton[] = [
  {
    id: 'pass',
    label: '通过',
    status: ReviewItemStatus.PASS,
    type: 'success',
    icon: 'CircleCheck',
    enabled: true,
    order: 1,
    action: {
      method: 'PUT',
      requireComment: false,
      successMessage: '标记为通过',
      payloadTemplate: {
        status: 'PASS',
        reviewer: '${currentUser}',
        reviewTime: '${currentTime}',
        updateLocal: true,
        deferSubmit: true
      }
    }
  },
  {
    id: 'conditional-pass',
    label: '条件通过',
    status: 'CONDITIONAL_PASS',
    type: 'warning',
    icon: 'Warning',
    enabled: true,
    order: 2,
    action: {
      method: 'PUT',
      requireComment: true,
      confirmMessage: '确认标记为条件通过？',
      successMessage: '标记为条件通过',
      payloadTemplate: {
        status: 'CONDITIONAL_PASS',
        comment: '${comment}',
        reviewer: '${currentUser}',
        reviewTime: '${currentTime}',
        conditions: '${customFields.conditions}',
        updateLocal: true,
        deferSubmit: true
      },
      postActions: [
        {
          type: 'sendNotification',
          config: {
            recipients: ['${customFields.assignee}'],
            template: 'conditional-pass-notification'
          }
        }
      ]
    }
  },
  {
    id: 'partial-pass',
    label: '部分通过',
    status: 'PARTIAL_PASS',
    type: 'info',
    icon: 'SemiSelect',
    enabled: true,
    order: 3,
    action: {
      method: 'PUT',
      requireComment: true,
      successMessage: '标记为部分通过',
      payloadTemplate: {
        status: 'PARTIAL_PASS',
        comment: '${comment}',
        reviewer: '${currentUser}',
        reviewTime: '${currentTime}',
        partialDetails: '${customFields.partialDetails}',
        updateLocal: true,
        deferSubmit: true
      }
    }
  },
  {
    id: 'fail',
    label: '不通过',
    status: ReviewItemStatus.FAIL,
    type: 'danger',
    icon: 'CircleClose',
    enabled: true,
    order: 4,
    action: {
      method: 'PUT',
      requireComment: true,
      confirmMessage: '确认标记为不通过？',
      successMessage: '标记为不通过',
      payloadTemplate: {
        status: 'FAIL',
        comment: '${comment}',
        reviewer: '${currentUser}',
        reviewTime: '${currentTime}',
        updateLocal: true,
        deferSubmit: true
      },
      postActions: [
        {
          type: 'generateDefect',
          config: {
            ruleId: 'fail-auto-defect',
            autoConfirm: false
          }
        }
      ]
    }
  },
  {
    id: 'skip',
    label: '跳过',
    status: ReviewItemStatus.SKIP,
    type: 'info',
    icon: 'Remove',
    enabled: true,
    order: 5,
    action: {
      method: 'PUT',
      requireComment: false,
      successMessage: '已跳过',
      payloadTemplate: {
        status: 'SKIP',
        reviewer: '${currentUser}',
        reviewTime: '${currentTime}',
        updateLocal: true,
        deferSubmit: true
      }
    }
  }
]

// 默认状态按钮组
export const DEFAULT_BUTTON_GROUP: StatusButtonGroup = {
  id: 'default',
  name: '默认状态按钮组',
  description: '包含所有基础状态操作',
  buttons: DEFAULT_STATUS_BUTTONS,
  defaultButtons: ['pass', 'conditional-pass', 'fail', 'skip'],
  layout: 'horizontal'
}

// 工具函数：模板变量替换
export function replaceTemplateVariables(
  template: string, 
  item: ExtendedReviewItem, 
  context: Record<string, any> = {}
): string {
  let result = template
  
  // 替换基础字段
  const fields: Record<string, any> = {
    itemId: item.itemId,
    sequence: item.sequence,
    content: item.content,
    category: item.category,
    status: item.status,
    comment: item.comment || '',
    reviewer: item.reviewer || '',
    reviewTime: item.reviewTime || '',
    required: item.required ? '是' : '否'
  }
  
  // 替换自定义字段
  if (item.customFields) {
    Object.keys(item.customFields).forEach(key => {
      fields[`customFields.${key}`] = item.customFields![key]
    })
  }
  
  // 替换上下文变量
  Object.keys(context).forEach(key => {
    fields[key] = context[key]
  })
  
  // 执行替换
  Object.keys(fields).forEach(key => {
    const regex = new RegExp(`\\$\\{${key}\\}`, 'g')
    result = result.replace(regex, String(fields[key] || ''))
  })
  
  return result
}

// 工具函数：检查条件是否满足
export function checkConditions(
  conditions: DefectCondition[], 
  item: ExtendedReviewItem
): boolean {
  if (!conditions || conditions.length === 0) return true
  
  let result = true
  let currentLogic: 'AND' | 'OR' = 'AND'
  
  for (const condition of conditions) {
    const fieldValue = getFieldValue(item, condition.field)
    const conditionResult = evaluateCondition(fieldValue, condition)
    
    if (currentLogic === 'AND') {
      result = result && conditionResult
    } else {
      result = result || conditionResult
    }
    
    if (condition.logicOperator) {
      currentLogic = condition.logicOperator
    }
  }
  
  return result
}

// 获取字段值
function getFieldValue(item: ExtendedReviewItem, field: string): any {
  if (field.startsWith('customFields.')) {
    const customField = field.replace('customFields.', '')
    return item.customFields?.[customField]
  }
  
  return (item as any)[field]
}

// 评估单个条件
function evaluateCondition(fieldValue: any, condition: DefectCondition): boolean {
  const { operator, value } = condition
  
  switch (operator) {
    case 'equals':
      return fieldValue === value
    case 'contains':
      return String(fieldValue).includes(String(value))
    case 'startsWith':
      return String(fieldValue).startsWith(String(value))
    case 'endsWith':
      return String(fieldValue).endsWith(String(value))
    case 'regex':
      return new RegExp(String(value)).test(String(fieldValue))
    case 'exists':
      return fieldValue !== undefined && fieldValue !== null && fieldValue !== ''
    case 'notExists':
      return fieldValue === undefined || fieldValue === null || fieldValue === ''
    default:
      return false
  }
}

// 完成评审按钮配置
export interface CompletionButton {
  id: string
  label: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  enabled: boolean
  order: number

  // 显示条件脚本
  displayCondition: {
    script: string  // JavaScript脚本，返回boolean
    description: string  // 条件描述
  }

  // 按钮行为配置
  action: {
    method: 'POST' | 'PUT' | 'PATCH'
    confirmMessage?: string  // 确认消息
    successMessage?: string  // 成功消息

    // 请求参数模板
    payloadTemplate: Record<string, any>

    // 后续动作
    postActions?: PostAction[]
  }
}

// 完成评审按钮组配置
export interface CompletionButtonGroup {
  id: string
  name: string
  description: string
  buttons: CompletionButton[]

  // 脚本执行上下文配置
  scriptContext: {
    allowedFunctions: string[]  // 允许的函数列表
    timeout: number  // 脚本执行超时时间（毫秒）
  }
}

// 默认完成评审按钮
export const DEFAULT_COMPLETION_BUTTONS: CompletionButton[] = [
  {
    id: 'complete-pass',
    label: '通过并完成',
    type: 'success',
    icon: 'CircleCheck',
    enabled: true,
    order: 1,
    displayCondition: {
      script: `
        // 检查是否所有必填项都已完成
        const requiredItems = reviewItems.filter(item => item.required);
        const completedRequired = requiredItems.filter(item =>
          item.status === 'PASS' || item.status === 'CONDITIONAL_PASS'
        );

        // 检查是否有不通过的项目
        const failedItems = reviewItems.filter(item => item.status === 'FAIL');

        return completedRequired.length === requiredItems.length && failedItems.length === 0;
      `,
      description: '所有必填项通过且无不通过项时显示'
    },
    action: {
      method: 'PUT',
      confirmMessage: '确认通过并完成评审？',
      successMessage: '评审已通过并完成',
      payloadTemplate: {
        status: 'COMPLETED',
        result: 'PASS',
        completedBy: '${currentUser}',
        completedTime: '${currentTime}',
        summary: '评审通过，所有检查项符合要求'
      }
    }
  },
  {
    id: 'complete-conditional',
    label: '条件通过并完成',
    type: 'warning',
    icon: 'Warning',
    enabled: true,
    order: 2,
    displayCondition: {
      script: `
        // 检查是否有条件通过的项目
        const conditionalItems = reviewItems.filter(item =>
          item.status === 'CONDITIONAL_PASS'
        );

        // 检查是否所有必填项都已处理
        const requiredItems = reviewItems.filter(item => item.required);
        const processedRequired = requiredItems.filter(item =>
          item.status !== 'PENDING'
        );

        return conditionalItems.length > 0 && processedRequired.length === requiredItems.length;
      `,
      description: '存在条件通过项且所有必填项已处理时显示'
    },
    action: {
      method: 'PUT',
      confirmMessage: '确认条件通过并完成评审？',
      successMessage: '评审已条件通过并完成',
      payloadTemplate: {
        status: 'COMPLETED',
        result: 'CONDITIONAL_PASS',
        completedBy: '${currentUser}',
        completedTime: '${currentTime}',
        summary: '评审条件通过，存在需要关注的项目'
      }
    }
  },
  {
    id: 'complete-fail',
    label: '不通过并完成',
    type: 'danger',
    icon: 'CircleClose',
    enabled: true,
    order: 3,
    displayCondition: {
      script: `
        // 检查是否有不通过的项目
        const failedItems = reviewItems.filter(item => item.status === 'FAIL');

        return failedItems.length > 0;
      `,
      description: '存在不通过项时显示'
    },
    action: {
      method: 'PUT',
      confirmMessage: '确认不通过并完成评审？',
      successMessage: '评审已标记为不通过并完成',
      payloadTemplate: {
        status: 'COMPLETED',
        result: 'FAIL',
        completedBy: '${currentUser}',
        completedTime: '${currentTime}',
        summary: '评审不通过，存在不符合要求的项目'
      }
    }
  }
]

// 默认完成评审按钮组
export const DEFAULT_COMPLETION_BUTTON_GROUP: CompletionButtonGroup = {
  id: 'default-completion',
  name: '默认完成评审按钮组',
  description: '根据评审结果动态显示不同的完成按钮',
  buttons: DEFAULT_COMPLETION_BUTTONS,
  scriptContext: {
    allowedFunctions: ['filter', 'map', 'reduce', 'find', 'some', 'every'],
    timeout: 5000
  }
}
