package com.checklist.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

/**
 * 完成评审按钮配置
 */
public class CompletionButton {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("label")
    private String label;
    
    @JsonProperty("type")
    private String type; // primary, success, warning, danger, info
    
    @JsonProperty("icon")
    private String icon;
    
    @JsonProperty("enabled")
    private boolean enabled;
    
    @JsonProperty("order")
    private int order;
    
    @JsonProperty("displayCondition")
    private DisplayCondition displayCondition;
    
    @JsonProperty("action")
    private ButtonAction action;
    
    // 构造函数
    public CompletionButton() {}
    
    public CompletionButton(String id, String label, String type, String icon, 
                           boolean enabled, int order, DisplayCondition displayCondition, 
                           ButtonAction action) {
        this.id = id;
        this.label = label;
        this.type = type;
        this.icon = icon;
        this.enabled = enabled;
        this.order = order;
        this.displayCondition = displayCondition;
        this.action = action;
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getLabel() { return label; }
    public void setLabel(String label) { this.label = label; }
    
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    public String getIcon() { return icon; }
    public void setIcon(String icon) { this.icon = icon; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public int getOrder() { return order; }
    public void setOrder(int order) { this.order = order; }
    
    public DisplayCondition getDisplayCondition() { return displayCondition; }
    public void setDisplayCondition(DisplayCondition displayCondition) { this.displayCondition = displayCondition; }
    
    public ButtonAction getAction() { return action; }
    public void setAction(ButtonAction action) { this.action = action; }
    
    /**
     * 显示条件配置
     */
    public static class DisplayCondition {
        @JsonProperty("script")
        private String script;
        
        @JsonProperty("description")
        private String description;
        
        public DisplayCondition() {}
        
        public DisplayCondition(String script, String description) {
            this.script = script;
            this.description = description;
        }
        
        public String getScript() { return script; }
        public void setScript(String script) { this.script = script; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
    
    /**
     * 按钮行为配置
     */
    public static class ButtonAction {
        @JsonProperty("method")
        private String method;
        
        @JsonProperty("confirmMessage")
        private String confirmMessage;
        
        @JsonProperty("successMessage")
        private String successMessage;
        
        @JsonProperty("payloadTemplate")
        private Map<String, Object> payloadTemplate;
        
        @JsonProperty("postActions")
        private List<PostAction> postActions;
        
        public ButtonAction() {}
        
        public ButtonAction(String method, String confirmMessage, String successMessage,
                           Map<String, Object> payloadTemplate, List<PostAction> postActions) {
            this.method = method;
            this.confirmMessage = confirmMessage;
            this.successMessage = successMessage;
            this.payloadTemplate = payloadTemplate;
            this.postActions = postActions;
        }
        
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        
        public String getConfirmMessage() { return confirmMessage; }
        public void setConfirmMessage(String confirmMessage) { this.confirmMessage = confirmMessage; }
        
        public String getSuccessMessage() { return successMessage; }
        public void setSuccessMessage(String successMessage) { this.successMessage = successMessage; }
        
        public Map<String, Object> getPayloadTemplate() { return payloadTemplate; }
        public void setPayloadTemplate(Map<String, Object> payloadTemplate) { this.payloadTemplate = payloadTemplate; }
        
        public List<PostAction> getPostActions() { return postActions; }
        public void setPostActions(List<PostAction> postActions) { this.postActions = postActions; }
    }
    
    /**
     * 后续动作配置
     */
    public static class PostAction {
        @JsonProperty("type")
        private String type;
        
        @JsonProperty("config")
        private Map<String, Object> config;
        
        public PostAction() {}
        
        public PostAction(String type, Map<String, Object> config) {
            this.type = type;
            this.config = config;
        }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public Map<String, Object> getConfig() { return config; }
        public void setConfig(Map<String, Object> config) { this.config = config; }
    }
}
